#include "main.h"
#include "robodash/views/selector.hpp"

/**
 * Runs initialization code. This occurs as soon as the program is started.
 *
 * All other competition modes are blocked by initialize; it is recommended
 * to keep execution time for this mode under a few seconds.
 */

rd::Selector autonSelector({
    {"redLeft", [](){ Robot.selectedAutonPath = "redLeft"; intake.allianceState = 2; }},
    {"redRight", [](){ Robot.selectedAutonPath = "redRight"; intake.allianceState = 2; }},
    {"blueLeft", [](){ Robot.selectedAutonPath = "blueLeft"; intake.allianceState = 1; }},
    {"blueRight", [](){ Robot.selectedAutonPath = "blueRight"; intake.allianceState = 1; }},
    {"skills", [](){ Robot.selectedAutonPath = "skills"; intake.allianceState = 0; }}
});
rd_view_t* view = rd_view_create("Odometry & Values");
void initialize() {
    //Initialization
        Robot.state = 0;

        Robot.robotOdomDrivetrain.drivetrain.calibrate();
        Robot.robotOdomDrivetrain.drivetrain.setPose(0, 0, 0);
        //Add custom localization setPose

    //Screen Code
        pros::Task screenTask([](){
            //create label on brain screen to display robot location
            lv_obj_t* label = lv_label_create(rd_view_obj(view));
            lv_obj_align(label, LV_ALIGN_CENTER, 0, 0);
            while (true) {
                //Display robot information on brain screen
                const std::string output = fmt::format(
                    "Odometry: \nX: {}\nY: {}\nTheta: {}\nHorizontal Rotation Sensor Value: {}\nVertical Rotation Sensor Value: {}",
                    Robot.robotOdomDrivetrain.drivetrain.getPose().x, //eventually change pose data to custom localization
                    Robot.robotOdomDrivetrain.drivetrain.getPose().y, //eventually change pose data to custom localization
                    Robot.robotOdomDrivetrain.drivetrain.getPose().theta, //eventually change pose data to custom localization
                    Robot.robotOdomDrivetrain.horizontalRotationSensor.get_position(),
                    Robot.robotOdomDrivetrain.verticalRotationSensor.get_position()
                );
                lv_label_set_text(label, output.c_str());
                //Log telemetry
                lemlib::telemetrySink()-> info("Chassis pose: {}", Robot.robotOdomDrivetrain.drivetrain.getPose());
                pros::delay(10);
            }
        });
}

/**
 * Runs while the robot is in the disabled state of Field Management System or
 * the VEX Competition Switch, following either autonomous or opcontrol. When
 * the robot is enabled, this task will exit.
 */
void disabled() {}

/**
 * Runs after initialize(), and before autonomous when connected to the Field
 * Management System or the VEX Competition Switch. This is intended for
 * competition-specific initialization routines, such as an autonomous selector
 * on the LCD.
 *
 * This task will exit when the robot is enabled and autonomous or opcontrol
 * starts.
 */
void competition_initialize() {}

/**
 * Runs the user autonomous code. This function will be started in its own task
 * with the default priority and stack size whenever the robot is enabled via
 * the Field Management System or the VEX Competition Switch in the autonomous
 * mode. Alternatively, this function may be called in initialize or opcontrol
 * for non-competition testing purposes.
 *
 * If the robot is disabled or communications is lost, the autonomous task
 * will be stopped. Re-enabling the robot will restart the task, not re-start it
 * from where it left off.
 */
void autonomous() {
    autonSelector.run_auton();
    Robot.state = 2;
    Robot.startOperatingCycle();
}

/**
 * Runs the operator control code. This function will be started in its own task
 * with the default priority and stack size whenever the robot is enabled via
 * the Field Management System or the VEX Competition Switch in the operator
 * control mode.
 *
 * If no competition control is connected, this function will run immediately
 * following initialize().
 *
 * If the robot is disabled or communications is lost, the
 * operator control task will be stopped. Re-enabling the robot will restart the
 * task, not resume it from where it left off.
 */
void opcontrol() {
    Robot.state = 1;
    Robot.startOperatingCycle();
}