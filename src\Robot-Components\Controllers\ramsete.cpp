#include "Robot-Components/Controllers/ramsete.h"

// Constructor
RAMSETE::RAMSETE(const RAMSETEParams& params) :
    params_(params),
    initialized_(false),
    path_loaded_(false),
    start_time_(0.0),
    current_segment_index_(0),
    mpc_controller_(nullptr),
    mpc_initialized_(false),
    total_path_time_(0.0) {
}

// Initialize the RAMSETE controller
bool RAMSETE::initialize(const MPC::MPCParams& mpc_params) {
    try {
        // Validate RAMSETE parameters
        if (!validateParameters()) {
            initialized_ = false;
            return false;
        }

        // Initialize MPC controller if integration is enabled
        if (params_.enable_mpc_integration) {
            mpc_controller_ = std::make_unique<MPC>(mpc_params);
            mpc_initialized_ = mpc_controller_->initialize();

            if (!mpc_initialized_) {
                return false;
            }
        }

        initialized_ = true;
        return true;
    } catch (const std::exception& e) {
        initialized_ = false;
        return false;
    }
}

// Load path from LemLib Tarball file (loads first available path)
bool RAMSETE::loadPath(const std::string& file_path) {
    try {
        TarballData tarball_data = parseTarballFile(file_path);

        if (!tarball_data.is_valid || tarball_data.paths.empty()) {
            path_loaded_ = false;
            return false;
        }

        // Load the first available path
        current_path_ = tarball_data.paths.begin()->second;

        if (!current_path_.is_valid || current_path_.trajectory.empty()) {
            path_loaded_ = false;
            return false;
        }

        // Compute curvature and time stamps
        computePathCurvature(current_path_.trajectory);
        computeTimeStamps(current_path_.trajectory);

        // Calculate total path time
        if (!current_path_.trajectory.empty()) {
            total_path_time_ = current_path_.trajectory.back().time;
        }

        path_loaded_ = true;
        return true;
    } catch (const std::exception& e) {
        path_loaded_ = false;
        return false;
    }
}

// Load specific named path from LemLib Tarball file
bool RAMSETE::loadPath(const std::string& file_path, const std::string& path_name) {
    try {
        TarballData tarball_data = parseTarballFile(file_path);

        if (!tarball_data.is_valid || tarball_data.paths.empty()) {
            path_loaded_ = false;
            return false;
        }

        // Find the specific path
        auto path_it = tarball_data.paths.find(path_name);
        if (path_it == tarball_data.paths.end()) {
            path_loaded_ = false;
            return false;
        }

        current_path_ = path_it->second;

        if (!current_path_.is_valid || current_path_.trajectory.empty()) {
            path_loaded_ = false;
            return false;
        }

        // Compute curvature and time stamps
        computePathCurvature(current_path_.trajectory);
        computeTimeStamps(current_path_.trajectory);

        // Calculate total path time
        if (!current_path_.trajectory.empty()) {
            total_path_time_ = current_path_.trajectory.back().time;
        }

        path_loaded_ = true;
        return true;

    } catch (const std::exception& e) {
        path_loaded_ = false;
        return false;
    }
}

// List available path names in a LemLib Tarball file
std::vector<std::string> RAMSETE::listAvailablePaths(const std::string& file_path) {
    std::vector<std::string> path_names;

    try {
        TarballData tarball_data = parseTarballFile(file_path);

        if (tarball_data.is_valid) {
            for (const auto& path_pair : tarball_data.paths) {
                path_names.push_back(path_pair.first);
            }
        }
    } catch (const std::exception& e) {
        // Return empty vector on error
    }

    return path_names;
}

// Load path from trajectory points directly
bool RAMSETE::loadPath(const std::vector<TrajectoryPoint>& trajectory) {
    try {
        if (trajectory.empty()) {
            path_loaded_ = false;
            return false;
        }

        current_path_.trajectory = trajectory;
        current_path_.is_valid = true;

        // Compute curvature and time stamps
        computePathCurvature(current_path_.trajectory);
        computeTimeStamps(current_path_.trajectory);

        // Calculate total path time
        total_path_time_ = current_path_.trajectory.back().time;

        path_loaded_ = true;
        return true;
    } catch (const std::exception& e) {
        path_loaded_ = false;
        return false;
    }
}

// Start following the loaded path
bool RAMSETE::startPathFollowing() {
    if (!initialized_ || !path_loaded_) {
        return false;
    }

    start_time_ = pros::millis() / 1000.0;  // Convert to seconds
    current_segment_index_ = 0;

    return true;
}

// Compute RAMSETE control output
RAMSETE::ControlOutput RAMSETE::computeControl(const lemlib::Pose& current_pose, double current_time) {
    ControlOutput output;

    if (!initialized_ || !path_loaded_) {
        return output;
    }

    // Get reference point
    double path_time = current_time - start_time_;
    TrajectoryPoint reference = getCurrentReference(path_time);

    // Check if at goal
    output.at_goal = isAtGoal(current_pose);
    if (output.at_goal) {
        return output;  // Return zero velocities
    }

    // Store reference for output
    output.reference_x = reference.x;
    output.reference_y = reference.y;

    // Compute errors
    double dx = reference.x - current_pose.x;
    double dy = reference.y - current_pose.y;

    // Transform errors to robot frame
    double theta_rad = degToRad(current_pose.theta);
    double cos_theta = cos(theta_rad);
    double sin_theta = sin(theta_rad);

    double x_error = cos_theta * dx + sin_theta * dy;
    double y_error = -sin_theta * dx + cos_theta * dy;

    // Compute heading error
    double reference_heading = atan2(dy, dx) * 180.0 / M_PI;
    double heading_error = normalizeAngle(reference_heading - current_pose.theta);

    output.cross_track_error = y_error;
    output.heading_error = heading_error;

    // Convert reference velocity from path units to inches/sec
    double v_ref = convertVelocityUnits(reference.velocity);

    // RAMSETE controller equations
    double k = 2.0 * params_.zeta * sqrt(params_.b * v_ref * v_ref + reference.curvature * reference.curvature);

    output.linear_velocity = v_ref * cos(degToRad(heading_error)) + k * x_error;

    // RAMSETE angular velocity calculation
    // Use the correct RAMSETE formula: ω = ω_ref + k * sin(θ_e) * y_e / θ_e + b * v_ref * sin(θ_e)
    double theta_e_rad = degToRad(heading_error);
    double omega_ref = reference.curvature * v_ref;  // Reference angular velocity

    // Handle singularity when heading_error is near zero using sinc approximation
    if (std::abs(heading_error) < 0.1) {  // 0.1 degrees
        // Use sinc(x) ≈ 1 - x²/6 for small x to avoid division by zero
        double sinc_approx = 1.0 - (theta_e_rad * theta_e_rad) / 6.0;
        output.angular_velocity = radToDeg(omega_ref + k * sinc_approx * y_error + params_.b * v_ref * theta_e_rad);
    } else {
        // Standard RAMSETE formula
        double sinc_theta = sin(theta_e_rad) / theta_e_rad;
        output.angular_velocity = radToDeg(omega_ref + k * sinc_theta * y_error + params_.b * v_ref * sin(theta_e_rad));
    }

    // Apply velocity limits
    output.linear_velocity = std::max(-params_.max_linear_velocity,
                                     std::min(params_.max_linear_velocity, output.linear_velocity));
    output.angular_velocity = std::max(-params_.max_angular_velocity,
                                      std::min(params_.max_angular_velocity, output.angular_velocity));

    return output;
}

// Compute RAMSETE control output with MCL integration
RAMSETE::ControlOutput RAMSETE::computeControl(const lemlib::Pose& current_pose, double current_time,
                                              const Localization::PoseWithUncertainty& mcl_pose) {
    // Select pose based on MCL confidence
    lemlib::Pose selected_pose = selectPose(current_pose, mcl_pose);

    // Use standard control computation with selected pose
    return computeControl(selected_pose, current_time);
}

// Compute control with MPC integration
MPC::ControlCommand RAMSETE::computeControlWithMPC(const lemlib::Pose& current_pose, double current_time,
                                                   const std::optional<Localization::PoseWithUncertainty>& mcl_pose) {
    MPC::ControlCommand mpc_command;

    if (!initialized_ || !path_loaded_) {
        return mpc_command;
    }

    // Determine pose to use
    lemlib::Pose selected_pose = current_pose;
    if (mcl_pose.has_value() && params_.enable_mcl_integration) {
        selected_pose = selectPose(current_pose, mcl_pose.value());
    }

    if (params_.enable_mpc_integration && mpc_initialized_) {
        // Use MPC for control

        // Convert current pose to MPC state
        MPC::RobotState current_state;
        current_state.x = selected_pose.x;
        current_state.y = selected_pose.y;
        current_state.theta = selected_pose.theta;
        current_state.v = 0.0;  // Would need velocity estimation
        current_state.omega = 0.0;

        // Get RAMSETE reference for MPC trajectory generation
        ControlOutput ramsete_output = computeControl(selected_pose, current_time);

        // Convert RAMSETE output to MPC trajectory
        std::vector<MPC::TrajectoryPoint> mpc_trajectory = convertToMPCTrajectory(ramsete_output, current_time);

        // Set MPC trajectory
        if (!mpc_trajectory.empty()) {
            mpc_controller_->setReferenceTrajectory(mpc_trajectory);
        }

        // Compute MPC control
        if (mcl_pose.has_value()) {
            mpc_command = mpc_controller_->computeControl(current_state, current_time, mcl_pose.value());
        } else {
            mpc_command = mpc_controller_->computeControl(current_state, current_time);
        }
    } else {
        // Use RAMSETE directly and convert to MPC format
        ControlOutput ramsete_output = computeControl(selected_pose, current_time);

        mpc_command.linear_velocity = ramsete_output.linear_velocity;
        mpc_command.angular_velocity = ramsete_output.angular_velocity;
    }

    return mpc_command;
}

// Check if robot has reached the goal
bool RAMSETE::isAtGoal(const lemlib::Pose& current_pose) const {
    if (!path_loaded_ || current_path_.trajectory.empty()) {
        return false;
    }

    const TrajectoryPoint& goal = current_path_.trajectory.back();

    double dx = goal.x - current_pose.x;
    double dy = goal.y - current_pose.y;
    double distance = sqrt(dx * dx + dy * dy);

    return distance <= params_.goal_tolerance;
}

// Get current path progress
double RAMSETE::getPathProgress() const {
    if (!path_loaded_ || total_path_time_ <= 0.0) {
        return 0.0;
    }

    double current_time = pros::millis() / 1000.0 - start_time_;
    return std::min(1.0, std::max(0.0, current_time / total_path_time_));
}

// Get current reference point on the path
RAMSETE::TrajectoryPoint RAMSETE::getCurrentReference(double current_time) const {
    if (!path_loaded_ || current_path_.trajectory.empty()) {
        return TrajectoryPoint();
    }

    // Clamp time to path bounds
    current_time = std::max(0.0, std::min(total_path_time_, current_time));

    return interpolateReference(current_time);
}

// Reset the controller state
void RAMSETE::reset() {
    start_time_ = 0.0;
    current_segment_index_ = 0;

    if (mpc_controller_) {
        mpc_controller_->reset();
    }
}

// Update RAMSETE parameters
void RAMSETE::updateParameters(const RAMSETEParams& new_params) {
    params_ = new_params;
}

// Normalize angle to [-180, 180] degrees
double RAMSETE::normalizeAngle(double angle) const {
    while (angle > 180.0) angle -= 360.0;
    while (angle < -180.0) angle += 360.0;
    return angle;
}

// Parse LemLib Tarball format file
RAMSETE::TarballData RAMSETE::parseTarballFile(const std::string& file_path) {
    TarballData tarball_data;

    try {
        std::ifstream file(file_path);
        if (!file.is_open()) {
            return tarball_data;
        }

        std::string content((std::istreambuf_iterator<char>(file)),
                           std::istreambuf_iterator<char>());
        file.close();

        // Parse multiple path sections
        size_t pos = 0;
        while (pos < content.length()) {
            // Find next path section
            size_t start_marker = content.find("#PATH-POINTS-START ", pos);
            if (start_marker == std::string::npos) {
                break;
            }

            // Extract path name
            size_t name_start = start_marker + 19; // Length of "#PATH-POINTS-START "
            size_t name_end = content.find('\n', name_start);
            if (name_end == std::string::npos) {
                break;
            }
            std::string path_name = content.substr(name_start, name_end - name_start);

            // Find end of path section
            size_t data_start = name_end + 1;
            size_t data_end = content.find("endData", data_start);
            if (data_end == std::string::npos) {
                break;
            }

            // Extract path content
            std::string path_content = content.substr(data_start, data_end - data_start);

            // Parse this path section
            PathData path_data = parsePathSection(path_content);
            if (path_data.is_valid) {
                tarball_data.paths[path_name] = path_data;
            }

            pos = data_end + 7; // Move past "endData"
        }

        // Extract global metadata (JSON at the end)
        size_t metadata_start = content.find("#PATH.JERRYIO-DATA ");
        if (metadata_start != std::string::npos) {
            size_t json_start = metadata_start + 19; // Length of "#PATH.JERRYIO-DATA "
            tarball_data.global_metadata = content.substr(json_start);
        }

        tarball_data.is_valid = !tarball_data.paths.empty();

    } catch (const std::exception& e) {
        tarball_data.is_valid = false;
    }

    return tarball_data;
}

// Parse single path section from LemLib Tarball content
RAMSETE::PathData RAMSETE::parsePathSection(const std::string& content) {
    PathData path_data;

    try {
        std::istringstream stream(content);
        std::string line;
        std::vector<TrajectoryPoint> trajectory;

        // Read trajectory points
        while (std::getline(stream, line)) {
            // Skip empty lines and comments
            if (line.empty() || line[0] == '#') {
                continue;
            }

            std::stringstream ss(line);
            std::string x_str, y_str, vel_str;

            if (std::getline(ss, x_str, ',') &&
                std::getline(ss, y_str, ',') &&
                std::getline(ss, vel_str)) {

                try {
                    // Trim whitespace
                    x_str.erase(0, x_str.find_first_not_of(" \t"));
                    x_str.erase(x_str.find_last_not_of(" \t") + 1);
                    y_str.erase(0, y_str.find_first_not_of(" \t"));
                    y_str.erase(y_str.find_last_not_of(" \t") + 1);
                    vel_str.erase(0, vel_str.find_first_not_of(" \t"));
                    vel_str.erase(vel_str.find_last_not_of(" \t") + 1);

                    double x = std::stod(x_str);
                    double y = std::stod(y_str);
                    double velocity = std::stod(vel_str);

                    trajectory.emplace_back(x, y, velocity);
                } catch (const std::exception& e) {
                    // Skip invalid lines
                    continue;
                }
            }
        }

        if (!trajectory.empty()) {
            path_data.trajectory = trajectory;
            path_data.is_valid = true;
            // Set default values for LemLib Tarball format (supports bidirectional movement)
            path_data.max_velocity = 127.0;
            path_data.min_velocity = -127.0;  // Support negative velocities for backward movement
            path_data.velocity_step = 1.0;
        }

    } catch (const std::exception& e) {
        path_data.is_valid = false;
    }

    return path_data;
}

// Compute path curvature for trajectory points
void RAMSETE::computePathCurvature(std::vector<TrajectoryPoint>& trajectory) {
    if (trajectory.size() < 3) {
        // Not enough points to compute curvature
        for (auto& point : trajectory) {
            point.curvature = 0.0;
        }
        return;
    }

    // Set first and last points to zero curvature
    trajectory[0].curvature = 0.0;
    trajectory.back().curvature = 0.0;

    // Compute curvature for middle points using three-point method
    for (size_t i = 1; i < trajectory.size() - 1; ++i) {
        const auto& p1 = trajectory[i - 1];
        const auto& p2 = trajectory[i];
        const auto& p3 = trajectory[i + 1];

        // Compute vectors
        double dx1 = p2.x - p1.x;
        double dy1 = p2.y - p1.y;
        double dx2 = p3.x - p2.x;
        double dy2 = p3.y - p2.y;

        // Compute cross product and magnitudes
        double cross = dx1 * dy2 - dy1 * dx2;
        double mag1 = sqrt(dx1 * dx1 + dy1 * dy1);
        double mag2 = sqrt(dx2 * dx2 + dy2 * dy2);

        // Compute curvature
        if (mag1 > 1e-6 && mag2 > 1e-6) {
            trajectory[i].curvature = 2.0 * cross / (mag1 * mag2 * (mag1 + mag2));
        } else {
            trajectory[i].curvature = 0.0;
        }
    }
}

// Compute time stamps for trajectory points
void RAMSETE::computeTimeStamps(std::vector<TrajectoryPoint>& trajectory) {
    if (trajectory.empty()) {
        return;
    }

    trajectory[0].time = 0.0;

    for (size_t i = 1; i < trajectory.size(); ++i) {
        const auto& prev = trajectory[i - 1];
        const auto& curr = trajectory[i];

        // Compute distance
        double dx = curr.x - prev.x;
        double dy = curr.y - prev.y;
        double distance = sqrt(dx * dx + dy * dy);

        // Compute average velocity
        double avg_velocity = convertVelocityUnits((prev.velocity + curr.velocity) / 2.0);

        // Compute time increment
        double dt = (avg_velocity > 1e-6) ? distance / avg_velocity : 0.1;  // Default 0.1s if velocity is zero

        trajectory[i].time = prev.time + dt;
    }
}

// Find closest point on path to current position
size_t RAMSETE::findClosestPoint(const lemlib::Pose& current_pose) const {
    if (current_path_.trajectory.empty()) {
        return 0;
    }

    size_t closest_index = 0;
    double min_distance = std::numeric_limits<double>::max();

    for (size_t i = 0; i < current_path_.trajectory.size(); ++i) {
        const auto& point = current_path_.trajectory[i];
        double dx = point.x - current_pose.x;
        double dy = point.y - current_pose.y;
        double distance = sqrt(dx * dx + dy * dy);

        if (distance < min_distance) {
            min_distance = distance;
            closest_index = i;
        }
    }

    return closest_index;
}

// Interpolate reference point at given time
RAMSETE::TrajectoryPoint RAMSETE::interpolateReference(double time) const {
    if (current_path_.trajectory.empty()) {
        return TrajectoryPoint();
    }

    if (current_path_.trajectory.size() == 1) {
        return current_path_.trajectory[0];
    }

    // Find the segment containing the given time
    size_t i = 0;
    for (i = 0; i < current_path_.trajectory.size() - 1; ++i) {
        if (time <= current_path_.trajectory[i + 1].time) {
            break;
        }
    }

    // If time is beyond the last point, return the last point
    if (i >= current_path_.trajectory.size() - 1) {
        return current_path_.trajectory.back();
    }

    // Linear interpolation between points i and i+1
    const auto& p1 = current_path_.trajectory[i];
    const auto& p2 = current_path_.trajectory[i + 1];

    double dt = p2.time - p1.time;
    if (dt < 1e-6) {
        return p1;  // Avoid division by zero
    }

    double alpha = (time - p1.time) / dt;
    alpha = std::max(0.0, std::min(1.0, alpha));  // Clamp to [0, 1]

    TrajectoryPoint interpolated;
    interpolated.x = p1.x + alpha * (p2.x - p1.x);
    interpolated.y = p1.y + alpha * (p2.y - p1.y);
    interpolated.velocity = p1.velocity + alpha * (p2.velocity - p1.velocity);
    interpolated.time = time;
    interpolated.curvature = p1.curvature + alpha * (p2.curvature - p1.curvature);

    return interpolated;
}

// Convert velocity units to inches/sec
double RAMSETE::convertVelocityUnits(double velocity_units) const {
    // LemLib Tarball velocity units support bidirectional movement
    // Range: -127 to +127 (negative for backward, positive for forward)
    // Convert to inches/sec based on max velocity parameter
    double normalized = velocity_units / 127.0;  // Normalize to [-1, 1]
    return normalized * params_.max_linear_velocity;
}

// Determine which pose to use (MCL vs odometry)
lemlib::Pose RAMSETE::selectPose(const lemlib::Pose& odometry_pose,
                                const Localization::PoseWithUncertainty& mcl_pose) const {
    if (!params_.enable_mcl_integration || !shouldUseMCLPose(mcl_pose)) {
        return odometry_pose;
    }

    // Use MCL pose
    lemlib::Pose selected_pose(mcl_pose.x, mcl_pose.y, mcl_pose.theta);

    return selected_pose;
}

// Check if MCL pose should be used
bool RAMSETE::shouldUseMCLPose(const Localization::PoseWithUncertainty& mcl_pose) const {
    // Check confidence threshold
    if (mcl_pose.confidence < params_.mcl_confidence_threshold) {
        return false;
    }

    // Check if pose is within reasonable bounds (VEX field boundaries)
    if (mcl_pose.x < -12.0 || mcl_pose.x > 156.0 ||  // 144" field + 12" margin
        mcl_pose.y < -12.0 || mcl_pose.y > 156.0) {
        return false;
    }

    // Check if uncertainty is reasonable
    const double max_position_variance = 100.0;  // 10" standard deviation
    const double max_angle_variance = 900.0;     // 30° standard deviation

    if (mcl_pose.x_variance > max_position_variance ||
        mcl_pose.y_variance > max_position_variance ||
        mcl_pose.theta_variance > max_angle_variance) {
        return false;
    }

    return true;
}

// Convert RAMSETE output to MPC trajectory
std::vector<MPC::TrajectoryPoint> RAMSETE::convertToMPCTrajectory(const ControlOutput& ramsete_output,
                                                                  double current_time) const {
    std::vector<MPC::TrajectoryPoint> mpc_trajectory;

    // Create a short trajectory for MPC based on RAMSETE reference
    double dt = 1.0 / params_.mpc_update_rate;  // Time step based on MPC update rate
    int horizon_steps = 10;  // Short horizon for real-time performance

    for (int i = 0; i < horizon_steps; ++i) {
        double t = current_time + i * dt;
        TrajectoryPoint ref = getCurrentReference(t - start_time_);

        MPC::TrajectoryPoint mpc_point;
        mpc_point.x = ref.x;
        mpc_point.y = ref.y;

        // Compute heading from path direction
        if (i > 0) {
            double dx = ref.x - mpc_trajectory[i-1].x;
            double dy = ref.y - mpc_trajectory[i-1].y;
            if (dx != 0.0 || dy != 0.0) {
                mpc_point.theta = atan2(dy, dx) * 180.0 / M_PI;
            } else {
                mpc_point.theta = mpc_trajectory[i-1].theta;  // Use previous heading
            }
        } else {
            // For first point, look ahead to next point if available
            if (current_path_.trajectory.size() > 1) {
                TrajectoryPoint next_ref = getCurrentReference(t + dt - start_time_);
                double dx = next_ref.x - ref.x;
                double dy = next_ref.y - ref.y;
                if (dx != 0.0 || dy != 0.0) {
                    mpc_point.theta = atan2(dy, dx) * 180.0 / M_PI;
                } else {
                    mpc_point.theta = 0.0;  // Default heading
                }
            } else {
                mpc_point.theta = 0.0;  // Default heading
            }
        }

        mpc_point.v = convertVelocityUnits(ref.velocity);
        mpc_point.omega = 0.0;  // Will be computed by MPC
        mpc_point.time = t;

        mpc_trajectory.push_back(mpc_point);
    }

    return mpc_trajectory;
}

// Validate RAMSETE parameters
bool RAMSETE::validateParameters() const {
    // Check b parameter (convergence parameter, must be > 0)
    if (params_.b <= 0.0) {
        return false;
    }

    // Check zeta parameter (damping parameter, must be 0 < zeta < 1)
    if (params_.zeta <= 0.0 || params_.zeta >= 1.0) {
        return false;
    }

    // Check velocity limits
    if (params_.max_linear_velocity <= 0.0) {
        return false;
    }

    if (params_.max_angular_velocity <= 0.0) {
        return false;
    }

    // Check tolerance parameters
    if (params_.path_tolerance <= 0.0) {
        return false;
    }

    if (params_.goal_tolerance <= 0.0) {
        return false;
    }

    if (params_.velocity_tolerance < 0.0) {
        return false;
    }

    // Check lookahead time
    if (params_.lookahead_time <= 0.0) {
        return false;
    }

    // Check MCL integration parameters
    if (params_.enable_mcl_integration) {
        if (params_.mcl_confidence_threshold < 0.0 || params_.mcl_confidence_threshold > 1.0) {
            return false;
        }
    }

    // Check MPC integration parameters
    if (params_.enable_mpc_integration) {
        if (params_.mpc_update_rate <= 0.0) {
            return false;
        }
    }

    return true;
}

RAMSETE ramseteController;