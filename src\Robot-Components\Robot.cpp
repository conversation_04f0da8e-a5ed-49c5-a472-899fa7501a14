#include "Robot-Components/Robot.h"
#include "pros/misc.h"
#include "pros/rtos.hpp"

Robot::Robot(
    class baseDrivetrain& drivetrain_ref,
    class intake& intake_ref,
    class odomDrivetrain& odomDrivetrain_ref,
    class MCL& mclController_ref,
    class RAMSETE& ramseteController_ref,
    class MPC& mpcController_ref
):
    masterController(pros::E_CONTROLLER_MASTER), 
    robotBaseDrivetrain(drivetrain_ref),
    robotIntake(intake_ref),
    robotOdomDrivetrain(odomDrivetrain_ref),
    robotMclController(mclController_ref),
    robotRamseteController(ramseteController_ref),
    robotMpcController(mpcController_ref)
{};

void Robot::startOperatingCycle() {
    if(state == 1) {
        runDriverControl();
    };
    if(state == 2) {
        runAuton();
    };
}

void Robot::runDriverControl() {
    while(state == 1) {
        //Intake Control
            //state-based
                if(masterController.get_digital(pros::E_CONTROLLER_DIGITAL_A)) { robotIntake.intakeToBackpack(); };
                if(masterController.get_digital(pros::E_CONTROLLER_DIGITAL_A)) { robotIntake.backpackToMiddle(); };
                if(masterController.get_digital(pros::E_CONTROLLER_DIGITAL_A)) { robotIntake.backpackToTop(); };
                if(masterController.get_digital(pros::E_CONTROLLER_DIGITAL_A)) { robotIntake.directlyToTop(); };
            //Manual
                if(masterController.get_digital(pros::E_CONTROLLER_DIGITAL_UP)) { robotIntake.firstStage.move(127); };
                if(masterController.get_digital(pros::E_CONTROLLER_DIGITAL_DOWN)) { robotIntake.firstStage.move(-127); };

        //Mechanism Control
            //Mech 1

            //Mech 2
        
        //Driving
            robotBaseDrivetrain.leftSideMotors.move(masterController.get_analog(pros::E_CONTROLLER_ANALOG_LEFT_Y));
            robotBaseDrivetrain.rightSideMotors.move(masterController.get_analog(pros::E_CONTROLLER_ANALOG_RIGHT_Y));

        pros::delay(10);
    }
};

void Robot::runAuton() {
    // Initialize MCL and RAMSETE

    robotRamseteController.initialize();

    if(selectedAutonPath == "redLeft") {
        robotMclController.initialize(lemlib::Pose(-60, 46.667, 270), 1, 2, 3, 4);
        robotRamseteController.loadPath("../../static/redLeft.txt");
    };
    if(selectedAutonPath == "redRight") {
        robotMclController.initialize(lemlib::Pose(-60, -46.667, 270), 1, 2, 3, 4);
        robotRamseteController.loadPath("../../static/redRight.txt");
    };
    if(selectedAutonPath == "blueLeft") {
        robotMclController.initialize(lemlib::Pose(60, -46.667, 90), 1, 2, 3, 4);
        robotRamseteController.loadPath("../../static/blueLeft.txt");
    };
    if(selectedAutonPath == "blueRight") {
        robotMclController.initialize(lemlib::Pose(60, 46.667, 90), 1, 2, 3, 4);
        robotRamseteController.loadPath("../../static/blueRight.txt");
    };
    if(selectedAutonPath == "skills") {
        robotMclController.initialize(lemlib::Pose(0, 0, 0), 1, 2, 3, 4);
        robotRamseteController.loadPath("../../static/skills.txt");
    };

    robotRamseteController.startPathFollowing();

    pros::Task([this]() {
        Localization::PoseWithUncertainty currentPosition = robotMclController.getPose();
        lemlib::Pose lemlibCurrentPose(currentPosition.x, currentPosition.y, currentPosition.theta);
        while (!robotRamseteController.isAtGoal(lemlibCurrentPose)) {
            auto odom_pose = robotOdomDrivetrain.drivetrain.getPose();
            auto mcl_pose = robotMclController.getPose();
            double current_time = pros::millis() / 1000.0;
            
            // Get MPC control command with MCL integration
            auto mpc_control = robotRamseteController.computeControlWithMPC(
                odom_pose, current_time, mcl_pose);
            
            // Apply control to robot
            applyMPCControlToMotors(mpc_control);
            pros::delay(20);  // 50 Hz
        }
    });

    while(state == 2) {
        //check path interpolittion
        double currentPoint = robotRamseteController.getPathProgress();

        //if (at point 1) { do this };

        //if (at point 2) { do this };
        
        pros::delay(10);
    }
};

class Robot Robot(
    baseDrivetrain,
    intake,
    odomDrivetrain,
    mclController,
    ramseteController,
    mpcController
);